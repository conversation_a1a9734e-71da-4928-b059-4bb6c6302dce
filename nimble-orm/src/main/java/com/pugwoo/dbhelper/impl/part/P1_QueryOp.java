package com.pugwoo.dbhelper.impl.part;

import com.pugwoo.dbhelper.DBHelperDataService;
import com.pugwoo.dbhelper.DBHelperInterceptor;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.FillColumn;
import com.pugwoo.dbhelper.annotation.JoinTable;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import com.pugwoo.dbhelper.enums.FeatureEnum;
import com.pugwoo.dbhelper.exception.BadSQLSyntaxException;
import com.pugwoo.dbhelper.exception.InvalidParameterException;
import com.pugwoo.dbhelper.exception.NotAllowQueryException;
import com.pugwoo.dbhelper.exception.NotOnlyOneKeyColumnException;
import com.pugwoo.dbhelper.exception.NullKeyValueException;
import com.pugwoo.dbhelper.exception.RelatedColumnFieldNotFoundException;
import com.pugwoo.dbhelper.exception.SpringBeanNotMatchException;
import com.pugwoo.dbhelper.json.NimbleOrmJSON;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.dbhelper.sql.SQLAssert;
import com.pugwoo.dbhelper.sql.SQLUtils;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.dbhelper.utils.AnnotationSupportRowMapper;
import com.pugwoo.dbhelper.utils.DOInfoReader;
import com.pugwoo.dbhelper.utils.InnerCommonUtils;
import net.sf.jsqlparser.JSQLParserException;
import org.mvel2.MVEL;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Stream;

public abstract class P1_QueryOp extends P0_JdbcTemplateOp {

    @Override
    public <T> T getByKey(Class<T> clazz, Object keyValue) throws NullKeyValueException, NotOnlyOneKeyColumnException {
        assertNotVirtualTable(clazz);
        if (keyValue == null) {
            throw new NullKeyValueException();
        }
        SQLAssert.onlyOneKeyColumn(clazz);

        String where = SQLUtils.getKeysWhereSQLWithoutSoftDelete(getDatabaseType(), clazz);
        return getOne(clazz, where, keyValue);
    }

    @Override
    public <T> PageData<T> getPage(final Class<T> clazz, int page, int pageSize,
                                   String postSql, Object... args) {
        assertPage(page);
        if (maxPageSize != null && pageSize > maxPageSize) {
            LOGGER.warn("query class:{} pageSize {} is too large, set to maxPageSize {}", clazz, pageSize, maxPageSize);
            pageSize = maxPageSize;
        }

        int offset = (page - 1) * pageSize;
        return _getPage(clazz, true, offset, pageSize, postSql, args);
    }

    @Override
    public <T> PageData<T> getPage(Class<T> clazz, int page, int pageSize, WhereSQL whereSQL) {
        return whereSQL == null ? getPage(clazz, page, pageSize)
                : getPage(clazz, page, pageSize, whereSQL.getSQL(), whereSQL.getParams());
    }

    @Override
    public <T> PageData<T> getPage(final Class<T> clazz, int page, int pageSize) {
        return getPage(clazz, page, pageSize, "");
    }

    @Override
    public <T> long getCount(Class<T> clazz) {
        boolean isVirtualTable = DOInfoReader.isVirtualTable(clazz);

        String sql = SQLUtils.getSelectCountSQL(getDatabaseType(), clazz) +
                (isVirtualTable ? "" : SQLUtils.autoSetSoftDeleted(getDatabaseType(), "", clazz));

        Long rows = namedJdbcQueryForObject(Long.class, sql, new ArrayList<>());
        return rows == null ? 0 : rows;
    }

    // 为了解决group by的计数问题，将计数转换成select count(*) from (子select语句) 的形式
    @Override
    public <T> long getCount(Class<T> clazz, String postSql, Object... args) {
        boolean isVirtualTable = DOInfoReader.isVirtualTable(clazz);
        List<Object> argsList = InnerCommonUtils.arrayToList(args);

        String sql = "SELECT count(*) FROM ("
                + SQLUtils.getSelectSQL(getDatabaseType(), clazz, true, features, postSql)
                + (isVirtualTable ? (postSql == null ? "\n" : "\n" + postSql) : SQLUtils.autoSetSoftDeleted(getDatabaseType(), postSql, clazz))
                + ") tff305c6";

        Long rows = namedJdbcQueryForObject(Long.class, sql, argsList);
        return rows == null ? 0 : rows;
    }

    @Override
    public <T> long getCount(Class<T> clazz, WhereSQL whereSQL) {
        return whereSQL == null ? getCount(clazz) : getCount(clazz, whereSQL.getSQL(), whereSQL.getParams());
    }

    @Override
    public <T> PageData<T> getPageWithoutCount(Class<T> clazz, int page, int pageSize,
                                               String postSql, Object... args) {
        assertPage(page);
        if (maxPageSize != null && pageSize > maxPageSize) {
            LOGGER.warn("query class:{} pageSize {} is too large, set to maxPageSize {}", clazz, pageSize, maxPageSize);
            pageSize = maxPageSize;
        }

        int offset = (page - 1) * pageSize;
        return _getPage(clazz,  false, offset, pageSize, postSql, args);
    }

    @Override
    public <T> PageData<T> getPageWithoutCount(Class<T> clazz, int page, int pageSize, WhereSQL whereSQL) {
        return whereSQL == null ? getPageWithoutCount(clazz, page, pageSize)
                : getPageWithoutCount(clazz, page, pageSize, whereSQL.getSQL(), whereSQL.getParams());
    }

    @Override
    public <T> PageData<T> getPageWithoutCount(final Class<T> clazz, int page, int pageSize) {
        return getPageWithoutCount(clazz, page, pageSize, "");
    }

    @Override
    public <T> List<T> getAll(final Class<T> clazz) {
        return _getPage(clazz, false, null, null, "").getData();
    }

    @Override
    public <T> Stream<T> getAllForStream(Class<T> clazz) {
        return getAllForStream(clazz, "");
    }

    @Override
    public <T> Stream<T> getAllForStream(Class<T> clazz, String postSql, Object... args) {
        StringBuilder sqlSB = new StringBuilder();
        sqlSB.append(SQLUtils.getSelectSQL(getDatabaseType(), clazz, false, features, postSql));
        sqlSB.append(SQLUtils.autoSetSoftDeleted(getDatabaseType(), postSql, clazz));

        List<Object> argsList = InnerCommonUtils.arrayToList(args);
        doInterceptBeforeQuery(clazz, sqlSB, argsList);

        String sql = sqlSB.toString();
        Stream<T> list = namedJdbcQueryForStream(sql, argsList,
                new AnnotationSupportRowMapper<>(this, clazz, sql, argsList));
        // stream方式不支持doInterceptorAfterQueryList
        return handleStreamRelatedColumn(list, clazz);
    }

    @Override
    public <T> Stream<T> getAllForStream(Class<T> clazz, WhereSQL whereSQL) {
        return whereSQL == null ? getAllForStream(clazz) : getAllForStream(clazz, whereSQL.getSQL(), whereSQL.getParams());
    }

    @Override
    public <T> List<T> getAll(final Class<T> clazz, String postSql, Object... args) {
        return _getPage(clazz,  false, null, null, postSql, args).getData();
    }

    @Override
    public <T> List<T> getAll(Class<T> clazz, WhereSQL whereSQL) {
        return whereSQL == null ? getAll(clazz) : getAll(clazz, whereSQL.getSQL(), whereSQL.getParams());
    }

    @Override
    public <T> T getOne(Class<T> clazz) {
        List<T> list = _getPage(clazz, false, 0, 1, "").getData();
        return list == null || list.isEmpty() ? null : list.get(0);
    }

    @Override
    public <T> T getOne(Class<T> clazz, String postSql, Object... args) {
        List<T> list = _getPage(clazz,  false,
                0, 1, postSql, args).getData();
        return list == null || list.isEmpty() ? null : list.get(0);
    }

    @Override
    public <T> T getOne(Class<T> clazz, WhereSQL whereSQL) {
        return whereSQL == null ? getOne(clazz) : getOne(clazz, whereSQL.getSQL(), whereSQL.getParams());
    }

    @Override
    public <T> List<T> getRaw(Class<T> clazz, String sql, Map<String, ?> args) {
        sql = SQLUtils.removeEndSemicolon(sql);
        return getRawByNamedParam(clazz, sql, args);
    }

    @Override
    public <T> Stream<T> getRawForStream(Class<T> clazz, String sql, Map<String, ?> args) {
        sql = SQLUtils.removeEndSemicolon(sql);
        return getRawByNamedParamForStream(clazz, sql, args);
    }

    private <T> Stream<T> getRawByNamedParamForStream(Class<T> clazz, String sql, Map<String, ?> args) {
        List<Object> forIntercept = args == null ? new ArrayList<>() : InnerCommonUtils.newList(args);
        doInterceptBeforeQuery(clazz, sql, forIntercept);

        Stream<T> stream = namedJdbcQueryForStream(sql, args,
                new AnnotationSupportRowMapper<>(this, clazz, sql, forIntercept));

        // stream方式不支持doInterceptorAfterQueryList
        return handleStreamRelatedColumn(stream, clazz);
    }

    private <T> List<T> getRawByNamedParam(Class<T> clazz, String sql, Map<String, ?> args) {
        List<Object> forIntercept = new ArrayList<>();
        if (args != null) {
            forIntercept.add(args);
        }
        doInterceptBeforeQuery(clazz, sql, forIntercept);

        List<T> list = namedJdbcQuery(sql, args,
                new AnnotationSupportRowMapper<>(this, clazz, sql, forIntercept));

        handleRelatedColumn(list);
        handleFillColumn(list);
        doInterceptorAfterQueryList(clazz, list, -1, sql, forIntercept);

        return list;
    }

    @Override
    public <T> Stream<T> getRawForStream(Class<T> clazz, String sql, Object... args) {
        sql = SQLUtils.removeEndSemicolon(sql);
        if (args != null && args.length == 1 && args[0] instanceof Map) {
            LOGGER.error("getRawForStream(Class<T> clazz, String sql, Object... args) should not use Map as args");
            return getRawByNamedParamForStream(clazz, sql, (Map<String, ?>) args[0]);
        }

        List<Object> argsList = InnerCommonUtils.arrayToList(args);
        doInterceptBeforeQuery(clazz, sql, argsList);

        Stream<T> stream = namedJdbcQueryForStream(sql, argsList,
                new AnnotationSupportRowMapper<>(this, clazz, sql, argsList));

        // stream方式不支持doInterceptorAfterQueryList
        return handleStreamRelatedColumn(stream, clazz);
    }

    @Override
    public <T> List<T> getRaw(Class<T> clazz, String sql, Object... args) {
        sql = SQLUtils.removeEndSemicolon(sql);
        if (args != null && args.length == 1 && args[0] instanceof Map) {
            LOGGER.error("getRaw(Class<T> clazz, String sql, Object... args) should not use Map as args");
            return getRawByNamedParam(clazz, sql, (Map<String, ?>) args[0]);
        }

        List<Object> argsList = InnerCommonUtils.arrayToList(args);
        doInterceptBeforeQuery(clazz, sql, argsList);

        List<T> list = namedJdbcQuery(sql, argsList,
                new AnnotationSupportRowMapper<>(this, clazz, sql, argsList));
        handleRelatedColumn(list);
        handleFillColumn(list);

        doInterceptorAfterQueryList(clazz, list, -1, sql, argsList);
        return list;
    }

    @Override
    public <T> T getRawOne(Class<T> clazz, String sql, Object... args) {
        List<T> raw = getRaw(clazz, sql, args);
        if (raw == null || raw.isEmpty()) {
            return null;
        }
        return raw.get(0);
    }

    @Override
    public <T> T getRawOne(Class<T> clazz, String sql, Map<String, ?> args) {
        List<T> raw = getRaw(clazz, sql, args);
        if (raw == null || raw.isEmpty()) {
            return null;
        }
        return raw.get(0);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public <T> List<T> getByExample(T t, int limit) {
        assertNotVirtualTable(t.getClass());

        Map<Field, String> filed2column = new HashMap<>();
        List<Field> declaredFields = DOInfoReader.getColumns(t.getClass());
        for (Field declaredField : declaredFields) {
            Column annotation = declaredField.getAnnotation(Column.class);
            if (annotation != null) {
                filed2column.put(declaredField, annotation.value());
            }
        }

        List<String> cols = new ArrayList<>();
        List<Object> args = new ArrayList<>();

        for (Map.Entry<Field, String> filed : filed2column.entrySet()) {
            Object value = DOInfoReader.getValue(filed.getKey(), t);
            if (value != null) {
                cols.add(filed.getValue());
                args.add(value);
            }
        }

        StringBuilder sql = new StringBuilder();
        if (!cols.isEmpty()) {
            sql.append(" WHERE ");
        }

        int size = cols.size();
        for (int i = 0; i < size; i++) {
            sql.append(SQLUtils.getColumnName(getDatabaseType(), cols.get(i))).append("=?");
            if (i != size - 1) {
                sql.append(" AND ");
            }
        }

        return (List<T>) _getPage(t.getClass(),
                false, null, limit, sql.toString(), args.toArray()).getData();
    }

    /**
     * 查询列表
     *
     * @param clazz 注解了@Table的类
     * @param withCount 是否计算总数
     * @param offset 从0开始，null时不生效；当offset不为null时，要求limit存在
     * @param limit null时不生效
     * @param postSql sql的where/group/order等sql语句
     * @param args 参数
     */
    private <T> PageData<T> _getPage(Class<T> clazz, boolean withCount,
                                     Integer offset, Integer limit,
                                     String postSql, Object... args) {
        boolean isVirtualTable = DOInfoReader.isVirtualTable(clazz);

        StringBuilder sqlSB = new StringBuilder();
        sqlSB.append(SQLUtils.getSelectSQL(getDatabaseType(), clazz, false, features, postSql));
        // 当limit不为null时，分页由orm内部控制，此时postSql不应该包含limit子句，这里尝试去除
        if (limit != null && !isVirtualTable) {
            try {
                boolean autoAddOrderForPagination = getFeatureStatus(FeatureEnum.AUTO_ADD_ORDER_FOR_PAGINATION);
                postSql = SQLUtils.removeLimitAndAddOrder(getDatabaseType(), postSql, autoAddOrderForPagination, clazz);
            } catch (Exception e) {
                LOGGER.error("removeLimitAndAddOrder fail for class:{}, postSql:{}",
                        clazz, postSql, e);
            }
        }
        sqlSB.append(isVirtualTable ? (postSql == null ? "\n" : "\n" + postSql) : SQLUtils.autoSetSoftDeleted(getDatabaseType(), postSql, clazz));
        sqlSB.append(SQLUtils.genLimitSQL(getDatabaseType(), offset, limit));

        List<Object> argsList = InnerCommonUtils.arrayToList(args);

        doInterceptBeforeQuery(clazz, sqlSB, argsList);

        String sql = sqlSB.toString();
        List<T> list = namedJdbcQuery(sql, argsList,
                new AnnotationSupportRowMapper<>(this, clazz, sql, argsList));

        long total = -1; // -1 表示没有查询总数，未知
        if (withCount) {
            // 如果offset为0且查询的list小于limit数量，说明总数就这么多了，不需要再查总数了
            if(offset != null && offset == 0 && limit != null && list.size() < limit) {
                total = list.size();
            } else {
                total = getCount(clazz, postSql, args);
            }
        }

        handleRelatedColumn(list);
        handleFillColumn(list);
        doInterceptorAfterQueryList(clazz, list, total, sql, argsList);

        PageData<T> pageData = new PageData<>();
        pageData.setData(list);
        pageData.setTotal(total);
        if (limit != null) {
            pageData.setPageSize(limit);
        }

        return pageData;
    }

    @Override
    public <T> boolean isExist(Class<T> clazz, String postSql, Object... args) {
        return getOne(clazz, postSql, args) != null;
    }

    @Override
    public <T> boolean isExist(Class<T> clazz, WhereSQL whereSQL) {
        return whereSQL == null ? isExist(clazz, "") : isExist(clazz, whereSQL.getSQL(), whereSQL.getParams());
    }

    //////////////////// 拦截器封装方法

    private void doInterceptBeforeQuery(Class<?> clazz, StringBuilder sql, List<Object> args) {
        doInterceptBeforeQuery(clazz, sql.toString(), args);
    }

    private void doInterceptBeforeQuery(Class<?> clazz, String sql, List<Object> args) {
        for (DBHelperInterceptor interceptor : interceptors) {
            boolean isContinue = interceptor.beforeSelect(clazz, sql, args);
            if (!isContinue) {
                throw new NotAllowQueryException("interceptor class:" + interceptor.getClass());
            }
        }
    }

    private <T> List<T> doInterceptorAfterQueryList(Class<?> clazz, List<T> list, long total,
                                                    String sql, List<Object> args) {
        for (int i = interceptors.size() - 1; i >= 0; i--) {
            list = interceptors.get(i).afterSelect(clazz, sql, args, list, total);
        }
        return list;
    }

    // ======================= 处理 RelatedColumn数据 ========================

    @Override
    public <T> void handleRelatedColumn(T t) {
        postHandleRelatedColumnSingle(t);
    }

    @Override
    public <T> void handleRelatedColumn(T t, String... relatedColumnProperties) {
        postHandleRelatedColumnSingle(t, relatedColumnProperties);
    }

    @Override
    public <T> void handleRelatedColumn(List<T> list) {
        postHandleRelatedColumn(list, false);
    }

    @Override
    public <T> void handleRelatedColumn(List<T> list, String... relatedColumnProperties) {
        postHandleRelatedColumn(list, false, relatedColumnProperties);
    }

    /**单个关联*/
    private <T> void postHandleRelatedColumnSingle(T t, String... relatedColumnProperties) {
        if (t == null) {
            return;
        }
        List<T> list = new ArrayList<>();
        list.add(t);

        postHandleRelatedColumn(list, false, relatedColumnProperties);
    }

    private <T> List<T> filterRelatedColumnConditional(List<T> tList, String conditional, Field field) {
        if (InnerCommonUtils.isBlank(conditional)) {
            return tList;
        }

        List<T> result = new ArrayList<>();
        for (T t : tList) {
            Map<String, Object> vars = new HashMap<>();
            vars.put("t", t);
            try {
                Object value = MVEL.eval(conditional, vars);
                if (value == null) {
                    LOGGER.error("execute conditional return null, script:{}, t:{}",
                            conditional, NimbleOrmJSON.toJsonNoException(t));
                } else {
                    if (value instanceof Boolean) {
                        if ((Boolean) value) {
                            result.add(t);
                        } else {
                            if (field.getType() == List.class) {
                                DOInfoReader.setValue(field, t, new ArrayList<>());
                            }
                        }
                    } else {
                        LOGGER.error("execute conditional return is not instance of Boolean, script:{}, t:{}",
                                conditional, NimbleOrmJSON.toJsonNoException(t));
                    }
                }
            } catch (Throwable e) {
                LOGGER.error("execute script fail: {}, t:{}", conditional, NimbleOrmJSON.toJsonNoException(t), e);
            }
        }

        return result;
    }

    /**批量关联，要求批量操作的都是相同的类*/
    private <T> void postHandleRelatedColumn(List<T> tList, boolean isFromJoin, String... relatedColumnProperties) {
        if (tList == null || tList.isEmpty()) {
            return;
        }
        Class<?> clazz = getElementClass(tList);
        if (clazz == null) {
            return;
        }

        JoinTable joinTable = DOInfoReader.getJoinTable(clazz);
        if (joinTable != null && !isFromJoin) { // 处理join的方式
            SQLAssert.allSameClass(tList);

            List<Object> list1 = new ArrayList<>();
            List<Object> list2 = new ArrayList<>();

            Field joinLeftTableField = DOInfoReader.getJoinLeftTable(clazz);
            Field joinRightTableField = DOInfoReader.getJoinRightTable(clazz);
            for (T t : tList) {
                Object obj1 = DOInfoReader.getValue(joinLeftTableField, t);
                if (obj1 != null) {
                    list1.add(obj1);
                }
                Object obj2 = DOInfoReader.getValue(joinRightTableField, t);
                if (obj2 != null) {
                    list2.add(obj2);
                }
            }

            postHandleRelatedColumn(list1, false);
            postHandleRelatedColumn(list2, false);
            postHandleRelatedColumn(tList, true);
            return;
        }

        List<Field> relatedColumns = DOInfoReader.getRelatedColumns(clazz);
        if (!relatedColumns.isEmpty()) { // 只有有relatedColumn才进行判断是否全是相同的类型
            SQLAssert.allSameClass(tList);
        } else {
            return; // 不需要处理了
        }

        for (Field field : relatedColumns) {

            // 只处理指定的field
            if (InnerCommonUtils.isNotEmpty(relatedColumnProperties)) {
                boolean isContain = InnerCommonUtils.isContains(field.getName(), relatedColumnProperties);
                if (!isContain) {
                    continue;
                }
            }

            RelatedColumn column = field.getAnnotation(RelatedColumn.class);
            // 根据conditional判断该RelatedColumn是否进行处理
            List<T> tListFiltered = filterRelatedColumnConditional(tList, column.conditional(), field);

            if (InnerCommonUtils.isBlank(column.localColumn())) {
                throw new RelatedColumnFieldNotFoundException("field:" + field.getName() + " localColumn is blank");
            }
            if (InnerCommonUtils.isBlank(column.remoteColumn())) {
                throw new RelatedColumnFieldNotFoundException("field:" + field.getName() + " remoteColumn is blank");
            }

            List<DOInfoReader.RelatedField> localField = DOInfoReader.getFieldByDBField(clazz, column.localColumn(), field);

            // 批量查询数据库，提高效率的关键
            Class<?> remoteDOClass;
            if (field.getType() == List.class) {
                remoteDOClass = DOInfoReader.getGenericFieldType(field);
            } else {
                remoteDOClass = field.getType();
            }

            List<DOInfoReader.RelatedField> remoteField = DOInfoReader.getFieldByDBField(remoteDOClass, column.remoteColumn(), field);

            Set<Object> values = new HashSet<>(); // 用于去重，同样适用于ArrayList
            for (T t : tListFiltered) {
                Object value = DOInfoReader.getValueForRelatedColumn(localField, t);
                if (value != null) {
                    values.add(value);
                }
            }

            if (values.isEmpty()) {
                // 不需要查询数据库，但是对List的，设置空List，确保list不会是null
                if (field.getType() == List.class) {
                    for (T t : tListFiltered) {
                        DOInfoReader.setValue(field, t, new ArrayList<>());
                    }
                }
                continue;
            }

            List<?> relateValues;
            if (column.dataService() != void.class &&
                    DBHelperDataService.class.isAssignableFrom(column.dataService())) {
                DBHelperDataService dataService = (DBHelperDataService)
                        applicationContext.getBean(column.dataService());
                List<Object> valuesList = new ArrayList<>(values);
                relateValues = dataService.get(valuesList, column, clazz, remoteDOClass);
            } else {
                String whereColumn = getWhereColumnForRelated(remoteField);
                // 这里不能用DBHelper是因为拦截器会被重复触发；其次也没必要，另外的DBHelper的实现也重新实现这个逻辑
                P1_QueryOp _dbHelper = this;
                if (InnerCommonUtils.isNotBlank(column.dbHelperBean())) {
                    String beanName = column.dbHelperBean().trim();
                    Object bean = applicationContext.getBean(beanName);
                    if (!(bean instanceof P1_QueryOp)) {
                        throw new SpringBeanNotMatchException("cannot find DBHelper bean: " + beanName
                                 + " or it is not type of SpringJdbcDBHelper");
                    } else {
                        _dbHelper = (P1_QueryOp) bean;
                    }
                }

                if (InnerCommonUtils.isBlank(column.extraWhere())) {
                    String inExpr = whereColumn + " in " + buildQuestionMark(values);
                    relateValues = _dbHelper.getAllForRelatedColumn(remoteDOClass, "where " + inExpr, values);
                } else {
                    // 如果extraWhere包含limit子句，那么只能降级为逐个处理，否则可以用批量处理的方式提高性能
                    try {
                        if (SQLUtils.isContainsLimit(column.extraWhere())) {
                            String eqExpr = whereColumn + "=?";
                            String where = SQLUtils.insertWhereAndExpression(getDatabaseType(), column.extraWhere(), eqExpr);
                            relateValues = _dbHelper.getAllForRelatedColumnBySingleValue(remoteDOClass, where, values);
                        } else {
                            String inExpr = whereColumn + " in " + buildQuestionMark(values);
                            String where = SQLUtils.insertWhereAndExpression(getDatabaseType(), column.extraWhere(), inExpr);
                            relateValues = _dbHelper.getAllForRelatedColumn(remoteDOClass, where, values);
                        }
                    } catch (JSQLParserException e) {
                        LOGGER.error("wrong RelatedColumn extraWhere:{}, ignore extraWhere", column.extraWhere());
                        throw new BadSQLSyntaxException(e);
                    }
                }
            }
            if (relateValues == null) {
                relateValues = new ArrayList<>();
            }

            if (field.getType() == List.class) {
                Map<Object, List<Object>> mapRemoteValues = new HashMap<>();
                Map<String, List<Object>> mapRemoteValuesString = new HashMap<>();
                for (Object obj : relateValues) {
                    Object oRemoteValue = DOInfoReader.getValueForRelatedColumn(remoteField, obj);
                    if (oRemoteValue == null) {continue;}

                    List<Object> oRemoteValueList = mapRemoteValues.computeIfAbsent(
                            oRemoteValue, k -> new ArrayList<>());
                    oRemoteValueList.add(obj);

                    List<Object> oRemoteValueListString = mapRemoteValuesString.computeIfAbsent(
                            oRemoteValue.toString(), k -> new ArrayList<>());
                    oRemoteValueListString.add(obj);
                }
                for (T t : tListFiltered) {
                    List<Object> valueList = new ArrayList<>();
                    Object oLocalValue = DOInfoReader.getValueForRelatedColumn(localField, t);
                    if (oLocalValue != null) {
                        List<Object> objRemoteList = mapRemoteValues.get(oLocalValue);
                        if (objRemoteList != null) {
                            valueList = objRemoteList;
                        } else {
                            List<Object> objRemoteStringList = mapRemoteValuesString.get(oLocalValue.toString());
                            if (objRemoteStringList != null) {
                                LOGGER.error("@RelatedColumn fields local:{},remote:{} is different classes. Use String compare.",
                                        localField, remoteField);
                                valueList = objRemoteStringList;
                            }
                        }
                    }
                    if (valueList.isEmpty()) { // 没有匹配数据时，当原字段有值，则不修改原来的值
                        if (DOInfoReader.getValue(field, t) == null) {
                            DOInfoReader.setValue(field, t, valueList);
                        }
                    } else {
                        DOInfoReader.setValue(field, t, valueList);
                    }
                }
            } else {
                Map<Object, Object> mapRemoteValues = new HashMap<>();
                Map<String, Object> mapRemoteValuesString = new HashMap<>();
                for (Object obj : relateValues) {
                    Object oRemoteValue = DOInfoReader.getValueForRelatedColumn(remoteField, obj);
                    if (oRemoteValue != null && !mapRemoteValues.containsKey(oRemoteValue)) {
                        mapRemoteValues.put(oRemoteValue, obj);
                        mapRemoteValuesString.put(oRemoteValue.toString(), obj);
                    }
                }
                for (T t : tListFiltered) {
                    Object oLocalValue = DOInfoReader.getValueForRelatedColumn(localField, t);
                    if (oLocalValue == null) {continue;}
                    
                    Object objRemote = mapRemoteValues.get(oLocalValue);
                    if (objRemote != null) {
                        DOInfoReader.setValue(field, t, objRemote);
                        continue;
                    }
                    Object objRemoteString = mapRemoteValuesString.get(oLocalValue.toString());
                    if (objRemoteString != null) {
                        LOGGER.error("@RelatedColumn fields local:{},remote:{} are different classes. Use String compare.",
                                localField, remoteField);
                        DOInfoReader.setValue(field, t, objRemoteString);
                    }
                }
            }
        }
    }
    
    // ======================= 处理 FillColumn数据 ========================
    
    @Override
    public <T> void handleFillColumn(T t) {
        postHandleFillColumnSingle(t);
    }
    
    @Override
    public <T> void handleFillColumn(T t, String... fillColumnProperties) {
        postHandleFillColumnSingle(t, fillColumnProperties);
    }
    
    @Override
    public <T> void handleFillColumn(List<T> list) {
        postHandleFillColumn(list);
    }
    
    @Override
    public <T> void handleFillColumn(List<T> list, String... fillColumnProperties) {
        postHandleFillColumn(list, fillColumnProperties);
    }
    
    /**单个填充*/
    private <T> void postHandleFillColumnSingle(T t, String... fillColumnProperties) {
        if (t == null) {
            return;
        }
        List<T> list = new ArrayList<>();
        list.add(t);
        
        postHandleFillColumn(list, fillColumnProperties);
    }
    
    /**批量填充，要求批量操作的都是相同的类*/
    private <T> void postHandleFillColumn(List<T> tList, String... fillColumnProperties) {
        if (tList == null || tList.isEmpty()) {
            return;
        }
        Class<?> clazz = getElementClass(tList);
        if (clazz == null) {
            return;
        }
        
        List<Field> fillColumns = DOInfoReader.getFillColumns(clazz);
        if (fillColumns.isEmpty()) {
            return; // 不需要处理了
        }
        
        SQLAssert.allSameClass(tList);
        
        for (Field field : fillColumns) {
            // 只处理指定的field
            if (InnerCommonUtils.isNotEmpty(fillColumnProperties)) {
                boolean isContain = InnerCommonUtils.isContains(field.getName(), fillColumnProperties);
                if (!isContain) {
                    continue;
                }
            }
            
            FillColumn fillColumn = field.getAnnotation(FillColumn.class);
            
            // 根据conditional判断该FillColumn是否进行处理
            List<T> tListFiltered = filterFillColumnConditional(tList, fillColumn.conditional(), field);
            
            if (InnerCommonUtils.isBlank(fillColumn.refField())) {
                LOGGER.error("field:{} refField is blank", field.getName());
                continue;
            }
            if (InnerCommonUtils.isBlank(fillColumn.fillScript())) {
                LOGGER.error("field:{} fillScript is blank", field.getName());
                continue;
            }
            
            // 解析refField，支持多个字段用逗号分隔
            String[] refFields = fillColumn.refField().split(",");
            for (int i = 0; i < refFields.length; i++) {
                refFields[i] = refFields[i].trim();
                if (refFields[i].isEmpty()) {
                    LOGGER.error("refField:{} contains empty field name", fillColumn.refField());
                }
            }
            
            // 为每个对象执行填充逻辑
            for (T t : tListFiltered) {
                try {
                    // 构建脚本参数
                    Map<String, Object> scriptVars = new HashMap<>();
                    
                    // 获取参考字段的值
                    for (int i = 0; i < refFields.length; i++) {
                        Object refValue = getRefFieldValue(t, refFields[i], clazz);
                        if (i == 0) {
                            scriptVars.put("refField", refValue);
                            scriptVars.put("refField1", refValue);
                        } else {
                            scriptVars.put("refField" + (i + 1), refValue);
                        }
                    }
                    
                    // 执行填充脚本
                    Object fillValue = MVEL.eval(fillColumn.fillScript(), scriptVars);
                    
                    // 设置填充值到目标字段
                    DOInfoReader.setValue(field, t, fillValue);
                    
                } catch (Throwable e) {
                    LOGGER.error("execute fillScript fail: {}, field: {}, object: {}",
                            fillColumn.fillScript(), field.getName(), NimbleOrmJSON.toJsonNoException(t), e);
                    if (!fillColumn.ignoreScriptError()) {
                        throw new RuntimeException("FillColumn script execution failed", e);
                    }
                }
            }
        }
    }
    
    private <T> List<T> filterFillColumnConditional(List<T> tList, String conditional, Field field) {
        if (InnerCommonUtils.isBlank(conditional)) {
            return tList;
        }
        
        List<T> result = new ArrayList<>();
        for (T t : tList) {
            Map<String, Object> vars = new HashMap<>();
            vars.put("t", t);
            try {
                Object value = MVEL.eval(conditional, vars);
                if (value == null) {
                    LOGGER.error("execute conditional return null, script:{}, t:{}",
                            conditional, NimbleOrmJSON.toJsonNoException(t));
                } else {
                    if (value instanceof Boolean) {
                        if ((Boolean) value) {
                            result.add(t);
                        }
                    } else {
                        LOGGER.error("execute conditional return is not instance of Boolean, script:{}, t:{}",
                                conditional, NimbleOrmJSON.toJsonNoException(t));
                    }
                }
            } catch (Throwable e) {
                LOGGER.error("execute script fail: {}, t:{}", conditional, NimbleOrmJSON.toJsonNoException(t), e);
            }
        }
        
        return result;
    }
    
    /**
     * 获取参考字段的值，支持数据库字段名和Java字段名
     */
    private Object getRefFieldValue(Object obj, String refFieldName, Class<?> clazz) {
        if (InnerCommonUtils.isBlank(refFieldName)) {
            return null;
        }

        // 首先尝试通过数据库字段名查找
        try {
            List<Field> columns = DOInfoReader.getColumns(clazz);
            for (Field field : columns) {
                Column column = field.getAnnotation(Column.class);
                if (column != null && column.value().equals(refFieldName)) {
                    return DOInfoReader.getValue(field, obj);
                }
            }
        } catch (Exception e) {
            LOGGER.debug("Failed to get value by column name: {}", refFieldName, e);
        }
        
        // 然后尝试通过Java字段名查找
        try {
            Field field = clazz.getDeclaredField(refFieldName);
            return DOInfoReader.getValue(field, obj);
        } catch (Exception e) {
            LOGGER.debug("Failed to get value by field name: {}", refFieldName, e);
        }
        
        // 最后尝试通过驼峰命名转换查找（如school_id -> schoolId）
        try {
            String camelCaseName = toCamelCase(refFieldName);
            Field field = clazz.getDeclaredField(camelCaseName);
            return DOInfoReader.getValue(field, obj);
        } catch (Exception e) {
            LOGGER.debug("Failed to get value by camel case field name: {}", refFieldName, e);
        }
        
        LOGGER.error("Cannot find ref field: {} in class: {}", refFieldName, clazz.getName());
        return null;
    }
    
    /**
     * 将下划线命名转换为驼峰命名
     */
    private String toCamelCase(String underscoreName) {
        if (underscoreName == null || underscoreName.isEmpty()) {
            return underscoreName;
        }
        
        StringBuilder result = new StringBuilder();
        boolean capitalizeNext = false;
        
        for (char c : underscoreName.toCharArray()) {
            if (c == '_') {
                capitalizeNext = true;
            } else {
                if (capitalizeNext) {
                    result.append(Character.toUpperCase(c));
                    capitalizeNext = false;
                } else {
                    result.append(Character.toLowerCase(c));
                }
            }
        }
        
        return result.toString();
    }
    
    
    /**获得用于查询remoteColumn的列，如果多个列时用加上()*/
    private String getWhereColumnForRelated(List<DOInfoReader.RelatedField> remoteField) {
        boolean isSingleColumn = remoteField.size() == 1;
        StringBuilder sb = new StringBuilder(isSingleColumn ? "" : "(");
        boolean isFirst = true;
        for (DOInfoReader.RelatedField remoteF : remoteField) {
            if (!isFirst) {
                sb.append(",");
            }
            isFirst = false;

            Column remoteColumn = remoteF.field.getAnnotation(Column.class);
            if (InnerCommonUtils.isBlank(remoteColumn.computed())) {
                sb.append(remoteF.fieldPrefix).append(SQLUtils.getColumnName(getDatabaseType(), remoteColumn.value()));
            } else {
                // 对于有remoteF.fieldPrefix的，由于计算列是用户自己写的，所以需要自己确保有fieldPrefix
                sb.append(SQLUtils.getComputedColumn(getDatabaseType(), remoteColumn, features));
            }
        }
        sb.append(isSingleColumn ? "" : ")");
        return sb.toString();
    }

    private String buildQuestionMark(Set<Object> values) {
        StringBuilder sb = new StringBuilder("(");
        boolean isFirst = true;
        for (Object obj : values) {
            if (!isFirst) {
                sb.append(",");
            }
            isFirst = false;

            if (obj instanceof List) {
                sb.append("(");
                int size = ((List<?>)obj).size();
                for (int i = 0; i < size; i++) {
                    if (i > 0) {
                        sb.append(",");
                    }
                    sb.append("?");
                }
                sb.append(")");
            } else {
                sb.append("?");
            }

        }
        sb.append(")");
        return sb.toString();
    }

    /**
     * 这个方法必须是protected，不可以是private，否则当dbhelper被AOP代理时，relatedColumn功能异常
     */
    protected <T> List<T> getAllForRelatedColumn(final Class<T> clazz, String postSql, Set<Object> values) {
        List<Object> param = new ArrayList<>();
        for (Object obj : values) {
            if (obj instanceof List) {
                param.addAll((List<?>) obj);
            } else {
                param.add(obj);
            }
        }

        return _getPage(clazz, false, null, null, postSql, param.toArray()).getData();
    }

    /**
     * 这个方法必须是protected，不可以是private，否则当dbhelper被AOP代理时，relatedColumn功能异常
     */
    protected <T> List<T> getAllForRelatedColumnBySingleValue(final Class<T> clazz, String postSql, Set<Object> values) {
        List<T> result = new ArrayList<>();

        for (Object value : values) {
            List<Object> param = new ArrayList<>();
            param.add(value);

            List<T> results = _getPage(clazz, false, null, null, postSql, param.toArray()).getData();
            result.addAll(results);
        }

        return result;
    }

    private Class<?> getElementClass(List<?> tList) {
        for (Object obj : tList) {
            if (obj != null) {
                return obj.getClass();
            }
        }
        return null;
    }

    private void assertNotVirtualTable(Class<?> clazz) {
        boolean isVirtualTable = DOInfoReader.isVirtualTable(clazz);
        if (isVirtualTable) {
            throw new NotAllowQueryException("Virtual table is not supported");
        }
    }

    private void assertPage(int page) {
        if (page < 1) {
            throw new InvalidParameterException("[page] must greater than 0");
        }
    }

    private <T> Stream<T> handleStreamRelatedColumn(Stream<T> stream, Class<T> clazz) {
        boolean hasRelatedColumn = !DOInfoReader.getRelatedColumns(clazz).isEmpty();
        boolean hasFillColumn = !DOInfoReader.getFillColumns(clazz).isEmpty();

        if (hasRelatedColumn || hasFillColumn) {
            return InnerCommonUtils.partition(stream, fetchSize).peek(list -> {
                if (hasRelatedColumn) {
                    handleRelatedColumn(list);
                }
                if (hasFillColumn) {
                    handleFillColumn(list);
                }
            }).flatMap(Collection::stream);
        } else {
            return stream;
        }
    }
}
